/* New Customer Page Styles */
.new-customer-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Section */
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  border-radius: 20px;
  margin-bottom: 32px;
  color: white;
  position: relative;
  overflow: hidden;
}

.header-content {
  flex: 1;
}

.form-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 12px 0;
  color: #fff;
}

.form-subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.5;
}

.header-decoration {
  display: flex;
  gap: 12px;
  align-items: center;
}

.decoration-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  animation: float 3s ease-in-out infinite;
}

.decoration-circle:nth-child(2) {
  animation-delay: 0.5s;
  width: 80px;
  height: 80px;
}

.decoration-circle:nth-child(3) {
  animation-delay: 1s;
  width: 50px;
  height: 50px;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Form Card */
.form-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 32px;
  border: 1px solid #e0e0e0;
}

.form-card-header {
  padding: 32px 40px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.form-card-header h3 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.form-card-header p {
  color: #666;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

/* Customer Form */
.customer-form {
  padding: 40px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32px;
  margin-bottom: 40px;
}

/* Form Group */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.label-icon {
  font-size: 16px;
}

.label-text {
  flex: 1;
}

.required-indicator {
  color: #f44336;
  font-weight: 700;
}

/* Input Wrapper */
.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  font-size: 16px;
  background: #fff;
  transition: all 0.3s ease;
  outline: none;
  color: #333;
}

.form-input:focus {
  border-color: #1e3c72;
  box-shadow: 0 0 0 3px rgba(30, 60, 114, 0.1);
}

.form-input:focus + .input-border {
  transform: scaleX(1);
}

.input-border {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #1e3c72, #2a5298);
  transform: scaleX(0);
  transition: transform 0.3s ease;
  border-radius: 0 0 12px 12px;
}

.input-helper {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
}

.btn-primary,
.btn-secondary {
  padding: 16px 32px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 160px;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  border: 2px solid transparent;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(30, 60, 114, 0.3);
}

.btn-primary:disabled {
  background: #90caf9;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-secondary {
  background: #fff;
  color: #666;
  border: 2px solid #e0e0e0;
}

.btn-secondary:hover {
  background: #f5f5f5;
  border-color: #bdbdbd;
  color: #333;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Loading Content */
.loading-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Info Cards */
.info-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-top: 32px;
}

.info-card {
  background: #fff;
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.info-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  border-color: rgba(30, 60, 114, 0.2);
}

.info-icon {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.info-card h4 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
}

.info-card p {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .new-customer-container {
    padding: 16px;
  }

  .form-header {
    padding: 32px 24px;
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }

  .form-title {
    font-size: 28px;
  }

  .customer-form {
    padding: 32px 24px;
  }

  .form-grid {
    gap: 24px;
  }

  .info-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .form-header {
    padding: 24px 20px;
  }

  .form-title {
    font-size: 24px;
  }

  .form-subtitle {
    font-size: 14px;
  }

  .header-decoration {
    display: none;
  }

  .form-card-header {
    padding: 24px 20px 20px;
  }

  .form-card-header h3 {
    font-size: 20px;
  }

  .customer-form {
    padding: 24px 20px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    margin-bottom: 32px;
  }

  .form-actions {
    flex-direction: column-reverse;
    gap: 12px;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    min-width: auto;
  }

  .info-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .info-card {
    padding: 20px;
  }

  .info-icon {
    font-size: 40px;
    margin-bottom: 12px;
  }

  .info-card h4 {
    font-size: 16px;
  }

  .info-card p {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .new-customer-container {
    padding: 12px;
  }

  .form-header {
    padding: 20px 16px;
    border-radius: 16px;
  }

  .form-title {
    font-size: 20px;
  }

  .form-card {
    border-radius: 12px;
  }

  .form-card-header {
    padding: 20px 16px 16px;
  }

  .customer-form {
    padding: 20px 16px;
  }

  .form-input {
    padding: 14px 16px;
    font-size: 16px;
  }

  .btn-primary,
  .btn-secondary {
    padding: 14px 24px;
    font-size: 14px;
  }

  .info-card {
    padding: 16px;
  }

  .info-icon {
    font-size: 36px;
  }
}

/* Legacy support for old class names */
.new_customer_form {
  max-width: 900px;
  margin: 0 auto;
}
