/* Login Page Styles */
.login-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  overflow: hidden;
}

/* Background Elements */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.water-drop {
  position: absolute;
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: waterDropFloat 6s ease-in-out infinite;
}

.water-drop:nth-child(1) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.water-drop:nth-child(2) {
  top: 60%;
  right: 15%;
  animation-delay: 2s;
  width: 15px;
  height: 15px;
}

.water-drop:nth-child(3) {
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
  width: 25px;
  height: 25px;
}

.water-wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  animation: waveMove 8s ease-in-out infinite;
}

@keyframes waterDropFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes waveMove {
  0%,
  100% {
    transform: translateX(0px);
  }
  50% {
    transform: translateX(20px);
  }
}

/* Login Card */
.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 40px;
  width: 90%;
  max-width: 450px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
  animation: cardSlideUp 0.6s ease-out;
}

@keyframes cardSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Login Header */
.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 24px;
}

.logo-icon {
  font-size: 32px;
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.login-logo h1 {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.login-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.login-subtitle {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* Login Form */
.login-form {
  margin-bottom: 24px;
}

.login-form-group {
  margin-bottom: 24px;
}

.login-form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.label-icon {
  font-size: 16px;
}

.label-text {
  flex: 1;
}

.password-label-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.forgot-password-link {
  font-size: 12px;
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password-link:hover {
  color: #764ba2;
  text-decoration: underline;
}

/* Input Wrapper */
.login-input-wrapper {
  position: relative;
}

.login-form-input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  font-size: 16px;
  background: #fff;
  transition: all 0.3s ease;
  outline: none;
  color: #333;
}

.login-form-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.login-form-input:focus + .password-toggle + .login-input-border,
.login-form-input:focus + .login-input-border {
  transform: scaleX(1);
}

.login-input-border {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
  border-radius: 0 0 12px 12px;
}

.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  transition: color 0.3s ease;
  z-index: 3;
}

.password-toggle:hover {
  color: #333;
}

/* Submit Button */
.login-submit-btn {
  width: 100%;
  padding: 16px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 8px;
}

.login-submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.login-submit-btn:disabled {
  background: linear-gradient(135deg, #b0b0b0 0%, #888 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Loading Content */
.login-loading-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.login-loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #fff;
  border-radius: 50%;
  animation: loginSpinnerSpin 1s linear infinite;
}

@keyframes loginSpinnerSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Login Footer */
.login-footer {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.login-footer p {
  font-size: 12px;
  color: #666;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-container {
    padding: 20px;
  }

  .login-card {
    padding: 32px 24px;
    max-width: 100%;
  }

  .login-logo h1 {
    font-size: 24px;
  }

  .login-title {
    font-size: 20px;
  }

  .logo-icon {
    font-size: 28px;
  }

  .water-drop {
    display: none;
  }

  .water-wave {
    height: 60px;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }

  .login-card {
    padding: 24px 20px;
    border-radius: 16px;
  }

  .login-header {
    margin-bottom: 24px;
  }

  .login-logo {
    margin-bottom: 16px;
  }

  .login-logo h1 {
    font-size: 20px;
  }

  .login-title {
    font-size: 18px;
  }

  .login-subtitle {
    font-size: 13px;
  }

  .login-form-group {
    margin-bottom: 20px;
  }

  .login-form-input {
    padding: 14px 16px;
    font-size: 16px;
  }

  .password-toggle {
    right: 12px;
  }

  .login-submit-btn {
    padding: 14px 20px;
    font-size: 14px;
  }
}

/* Legacy support for old auth styles */
.auth_bg {
  background: #0d1117;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.auth_bg .form_container {
  width: 90%;
  max-width: 288px;
}

.auth_bg .auth_heading {
  font-size: 24px;
  color: #f0f6fc;
  font-weight: 300;
  text-align: center;
  margin-bottom: 16px;
}

.auth_bg form {
  background: #151b23;
  color: #f0f6fc;
  border: 1px solid #3d444db3;
  padding: 16px;
  border-radius: 5px;
  font-size: 14px;
}

.auth_bg form .input_div {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}

.auth_bg form input {
  width: 100%;
  padding: 5px 12px;
  background-color: #0d1117 !important;
  border: 1px solid #3d444d;
  font-size: 14px;
  line-height: 20px;
  color: #f0f6fc;
  border-radius: 5px;
  margin-top: 8px;
}

.auth_bg a {
  color: #4493f8;
  font-size: 12px;
}

/* Forgot Password Page Styles */
.forgot-password-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  overflow: hidden;
}

/* Background Elements (reuse from login) */
.forgot-password-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

/* Forgot Password Card */
.forgot-password-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 40px;
  width: 90%;
  max-width: 450px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
  animation: cardSlideUp 0.6s ease-out;
}

/* Forgot Password Header */
.forgot-password-header {
  text-align: center;
  margin-bottom: 32px;
}

.forgot-password-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 24px;
}

.forgot-password-logo h1 {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.forgot-password-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.forgot-password-subtitle {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

/* Forgot Password Form */
.forgot-password-form {
  margin-bottom: 24px;
}

.forgot-password-form-group {
  margin-bottom: 24px;
}

.forgot-password-form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.forgot-password-input-wrapper {
  position: relative;
}

.forgot-password-form-input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  font-size: 16px;
  background: #fff;
  transition: all 0.3s ease;
  outline: none;
  color: #333;
}

.forgot-password-form-input:focus {
  border-color: #ff6b6b;
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

.forgot-password-form-input:focus + .forgot-password-input-border {
  transform: scaleX(1);
}

.forgot-password-input-border {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #ff6b6b, #ee5a24);
  transform: scaleX(0);
  transition: transform 0.3s ease;
  border-radius: 0 0 12px 12px;
}

.forgot-password-input-helper {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  display: block;
}

/* Submit Button */
.forgot-password-submit-btn {
  width: 100%;
  padding: 16px 24px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 8px;
}

.forgot-password-submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(255, 107, 107, 0.3);
}

.forgot-password-submit-btn:disabled {
  background: linear-gradient(135deg, #b0b0b0 0%, #888 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Loading Content */
.forgot-password-loading-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.forgot-password-loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #fff;
  border-radius: 50%;
  animation: forgotPasswordSpinnerSpin 1s linear infinite;
}

@keyframes forgotPasswordSpinnerSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Footer */
.forgot-password-footer {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.back-to-login-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #ff6b6b;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: color 0.3s ease;
}

.back-to-login-link:hover {
  color: #ee5a24;
  text-decoration: underline;
}

/* Success State */
.email-sent-success {
  text-align: center;
  padding: 20px 0;
}

.success-icon {
  font-size: 64px;
  margin-bottom: 24px;
  animation: successBounce 0.6s ease-out;
}

@keyframes successBounce {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.email-sent-success h3 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.email-sent-success p {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin: 0 0 32px 0;
}

.success-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.back-to-login-btn,
.send-again-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  border: none;
}

.back-to-login-btn {
  background: #fff;
  color: #ff6b6b;
  border: 2px solid #ff6b6b;
}

.back-to-login-btn:hover {
  background: #ff6b6b;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 107, 107, 0.3);
}

.send-again-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  border: 2px solid transparent;
}

.send-again-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(255, 107, 107, 0.3);
}

/* Responsive Design for Forgot Password */
@media (max-width: 768px) {
  .forgot-password-container {
    padding: 20px;
  }

  .forgot-password-card {
    padding: 32px 24px;
    max-width: 100%;
  }

  .forgot-password-logo h1 {
    font-size: 20px;
  }

  .forgot-password-title {
    font-size: 20px;
  }

  .logo-icon {
    font-size: 28px;
  }

  .success-actions {
    flex-direction: column;
    gap: 12px;
  }

  .back-to-login-btn,
  .send-again-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .forgot-password-container {
    padding: 16px;
  }

  .forgot-password-card {
    padding: 24px 20px;
    border-radius: 16px;
  }

  .forgot-password-header {
    margin-bottom: 24px;
  }

  .forgot-password-logo {
    margin-bottom: 16px;
  }

  .forgot-password-logo h1 {
    font-size: 18px;
  }

  .forgot-password-title {
    font-size: 18px;
  }

  .forgot-password-subtitle {
    font-size: 13px;
  }

  .forgot-password-form-group {
    margin-bottom: 20px;
  }

  .forgot-password-form-input {
    padding: 14px 16px;
    font-size: 16px;
  }

  .forgot-password-submit-btn {
    padding: 14px 20px;
    font-size: 14px;
  }

  .success-icon {
    font-size: 48px;
  }

  .email-sent-success h3 {
    font-size: 20px;
  }

  .email-sent-success p {
    font-size: 13px;
  }

  .back-to-login-btn,
  .send-again-btn {
    padding: 12px 20px;
    font-size: 13px;
  }
}
