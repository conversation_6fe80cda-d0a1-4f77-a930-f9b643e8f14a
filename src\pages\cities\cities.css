/* Cities Page Styles */

/* Loading State */
.cities-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid #61dafb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.cities-loading p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  margin: 0;
}

/* Error State */
.cities-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.cities-error h3 {
  color: #f44336;
  margin-bottom: 8px;
}

.cities-error p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 24px;
}

.retry-btn {
  background: #61dafb;
  color: #000;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #21a9c7;
  transform: translateY(-2px);
}

/* Header Section */
.cities-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40px;
  padding: 32px;
  background: linear-gradient(135deg, #3a3b3c 0%, #242526 100%);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content {
  flex: 1;
}

.cities-title {
  color: #fff;
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
}

.cities-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  margin: 0;
  line-height: 1.5;
}

.cities-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #61dafb;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 4px;
}

/* No Cities State */
.no-cities {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.no-cities-icon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.5;
}

.no-cities h3 {
  color: #fff;
  margin-bottom: 8px;
}

.no-cities p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

/* Cities Grid */
.cities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
}

/* City Card */
.city-card {
  background: #3a3b3c;
  border-radius: 16px;
  padding: 24px;
  text-decoration: none;
  color: #fff;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.city-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--status-color);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.city-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

.city-card:hover::before {
  opacity: 1;
}

/* City Card Header */
.city-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.city-icon {
  font-size: 32px;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.city-card:hover .city-icon {
  background: rgba(97, 218, 251, 0.2);
  transform: scale(1.1);
}

.city-status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--status-color);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

/* City Info */
.city-info {
  flex: 1;
}

.city-name {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
  color: #fff;
}

.city-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
}

/* City Stats Grid */
.city-stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.city-stat {
  background: #242526;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.city-card:hover .city-stat {
  border-color: rgba(255, 255, 255, 0.2);
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #fff;
  margin-bottom: 4px;
}

.stat-value.capacity {
  color: #2196f3;
}

.stat-value.fill-level {
  color: #ff9800;
}

.city-stat .stat-label {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0;
}

/* City Card Footer */
.city-card-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.view-details {
  color: #61dafb;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.city-card:hover .view-details {
  color: #21a9c7;
  transform: translateX(4px);
}

/* Status Colors */
.city-card.excellent {
  --status-color: #4caf50;
}

.city-card.good {
  --status-color: #8bc34a;
}

.city-card.moderate {
  --status-color: #ff9800;
}

.city-card.critical {
  --status-color: #f44336;
}

.city-card.no-tanks {
  --status-color: #9e9e9e;
}

/* Responsive Design */

/* For Tablets (768px - 1024px) */
@media (max-width: 1024px) {
  .cities-header {
    flex-direction: column;
    gap: 24px;
    padding: 24px;
  }

  .cities-title {
    font-size: 28px;
  }

  .cities-stats {
    gap: 24px;
    justify-content: center;
  }

  .cities-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }

  .city-card {
    padding: 20px;
    gap: 16px;
  }

  .city-name {
    font-size: 20px;
  }

  .city-stats-grid {
    gap: 12px;
  }

  .city-stat {
    padding: 12px;
  }

  .stat-value {
    font-size: 18px;
  }
}

/* For Mobile (max-width: 768px) */
@media (max-width: 768px) {
  .cities-header {
    padding: 20px;
    margin-bottom: 24px;
  }

  .cities-title {
    font-size: 24px;
  }

  .cities-subtitle {
    font-size: 14px;
  }

  .cities-stats {
    gap: 20px;
  }

  .stat-number {
    font-size: 24px;
  }

  .cities-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .city-card {
    padding: 16px;
    gap: 16px;
  }

  .city-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .city-icon {
    width: 48px;
    height: 48px;
    font-size: 24px;
  }

  .city-status-indicator {
    align-self: flex-end;
  }

  .city-name {
    font-size: 18px;
  }

  .city-description {
    font-size: 13px;
  }

  .city-stats-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .city-stat {
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
  }

  .stat-value {
    font-size: 16px;
    margin-bottom: 0;
  }

  .city-stat .stat-label {
    margin: 0;
  }

  .city-card-footer {
    padding-top: 12px;
  }

  .view-details {
    font-size: 13px;
  }

  .no-cities-icon {
    font-size: 48px;
  }

  .cities-loading,
  .cities-error {
    padding: 40px 20px;
  }
}

/* For Very Small Mobile (max-width: 480px) */
@media (max-width: 480px) {
  .cities-header {
    padding: 16px;
  }

  .cities-title {
    font-size: 20px;
  }

  .cities-stats {
    flex-direction: column;
    gap: 16px;
  }

  .city-card {
    padding: 12px;
  }

  .city-card-header {
    gap: 8px;
  }

  .city-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .city-name {
    font-size: 16px;
  }

  .stat-value {
    font-size: 14px;
  }

  .city-stat .stat-label {
    font-size: 10px;
  }
}

/* Legacy styles for backward compatibility */
.city_row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-bottom: 20px;
}

.city_div {
  background-color: #242526;
  color: #fff;
  border-radius: 10px;
  padding: 20px;
}

/* Legacy responsive */
@media (max-width: 1024px) {
  .city_row {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .city_row {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}
