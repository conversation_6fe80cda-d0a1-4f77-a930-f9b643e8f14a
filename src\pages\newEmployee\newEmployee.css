/* New Employee Page Styles */
.new-employee-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Section */
.employee-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40px;
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  border-radius: 20px;
  margin-bottom: 32px;
  color: white;
  position: relative;
  overflow: hidden;
}

.header-content {
  flex: 1;
}

.employee-form-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 12px 0;
  color: #fff;
}

.employee-form-subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.5;
}

.header-decoration {
  display: flex;
  gap: 12px;
  align-items: center;
}

.employee-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
  animation: avatarFloat 3s ease-in-out infinite;
}

.employee-avatar::before {
  content: "👤";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20px;
}

.employee-avatar:nth-child(2) {
  animation-delay: 0.5s;
  width: 60px;
  height: 60px;
}

.employee-avatar:nth-child(2)::before {
  font-size: 24px;
}

.employee-avatar:nth-child(3) {
  animation-delay: 1s;
  width: 45px;
  height: 45px;
}

.employee-avatar:nth-child(3)::before {
  font-size: 18px;
}

@keyframes avatarFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

/* Form Card */
.employee-form-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 32px;
  border: 1px solid #e0e0e0;
}

.employee-form-card-header {
  padding: 32px 40px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.employee-form-card-header h3 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.employee-form-card-header p {
  color: #666;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

/* Employee Form */
.employee-form {
  padding: 40px;
}

.employee-form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32px;
  margin-bottom: 40px;
}

/* Form Group */
.employee-form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.employee-form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.label-icon {
  font-size: 16px;
}

.label-text {
  flex: 1;
}

.required-indicator {
  color: #f44336;
  font-weight: 700;
}

/* Input Wrapper */
.employee-input-wrapper {
  position: relative;
}

.employee-form-input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  font-size: 16px;
  background: #fff;
  transition: all 0.3s ease;
  outline: none;
  color: #333;
}

.employee-form-input:focus {
  border-color: #2196f3;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.employee-form-input:focus + .employee-input-border {
  transform: scaleX(1);
}

.employee-input-border {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #2196f3, #1976d2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
  border-radius: 0 0 12px 12px;
}

.employee-input-helper {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

/* Form Actions */
.employee-form-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
}

.employee-btn-primary,
.employee-btn-secondary {
  padding: 16px 32px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 160px;
  justify-content: center;
}

.employee-btn-primary {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  border: 2px solid transparent;
}

.employee-btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(33, 150, 243, 0.3);
}

.employee-btn-primary:disabled {
  background: #90caf9;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.7;
}

.employee-btn-secondary {
  background: #fff;
  color: #666;
  border: 2px solid #e0e0e0;
}

.employee-btn-secondary:hover {
  background: #f5f5f5;
  border-color: #bdbdbd;
  color: #333;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Loading Content */
.employee-loading-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.employee-loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #fff;
  border-radius: 50%;
  animation: employeeSpinnerSpin 1s linear infinite;
}

@keyframes employeeSpinnerSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Info Cards */
.employee-info-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-top: 32px;
}

.employee-info-card {
  background: #fff;
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.employee-info-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  border-color: rgba(33, 150, 243, 0.2);
}

.info-icon {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.employee-info-card h4 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
}

.employee-info-card p {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .new-employee-container {
    padding: 16px;
  }

  .employee-form-header {
    padding: 32px 24px;
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }

  .employee-form-title {
    font-size: 28px;
  }

  .employee-form {
    padding: 32px 24px;
  }

  .employee-form-grid {
    gap: 24px;
  }

  .employee-info-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .employee-form-header {
    padding: 24px 20px;
  }

  .employee-form-title {
    font-size: 24px;
  }

  .employee-form-subtitle {
    font-size: 14px;
  }

  .header-decoration {
    display: none;
  }

  .employee-form-card-header {
    padding: 24px 20px 20px;
  }

  .employee-form-card-header h3 {
    font-size: 20px;
  }

  .employee-form {
    padding: 24px 20px;
  }

  .employee-form-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    margin-bottom: 32px;
  }

  .employee-form-actions {
    flex-direction: column-reverse;
    gap: 12px;
  }

  .employee-btn-primary,
  .employee-btn-secondary {
    width: 100%;
    min-width: auto;
  }

  .employee-info-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .employee-info-card {
    padding: 20px;
  }

  .info-icon {
    font-size: 40px;
    margin-bottom: 12px;
  }

  .employee-info-card h4 {
    font-size: 16px;
  }

  .employee-info-card p {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .new-employee-container {
    padding: 12px;
  }

  .employee-form-header {
    padding: 20px 16px;
    border-radius: 16px;
  }

  .employee-form-title {
    font-size: 20px;
  }

  .employee-form-card {
    border-radius: 12px;
  }

  .employee-form-card-header {
    padding: 20px 16px 16px;
  }

  .employee-form {
    padding: 20px 16px;
  }

  .employee-form-input {
    padding: 14px 16px;
    font-size: 16px;
  }

  .employee-btn-primary,
  .employee-btn-secondary {
    padding: 14px 24px;
    font-size: 14px;
  }

  .employee-info-card {
    padding: 16px;
  }

  .info-icon {
    font-size: 36px;
  }
}

/* Legacy support for old class names */
.new_employee_form {
  max-width: 900px;
  margin: 0 auto;
}
