.section-title {
  color: #fff;
  /* margin-bottom: 20px; */
  font-size: 24px;
  font-weight: 600;
}

/* Main Tank Section */
.main-tank-section {
  margin-bottom: 40px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pump-water-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.pump-water-btn:hover {
  background-color: #45a049;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.pump-water-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.pump-water-btn:disabled {
  background-color: #cccccc;
  color: #666666;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.main-tank-container {
  background-color: #242526;
  border-radius: 10px;
  padding: 20px;
  color: #fff;
}

/* New Tank Dashboard Layout */
.tank-dashboard-layout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  align-items: start;
}

.tank-overview-card {
  background: #3a3b3c;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tank-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.tank-header h3 {
  font-size: 24px;
  margin: 0;
  color: #61dafb;
  font-weight: 600;
}

.tank-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-high {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid #4caf50;
}

.status-medium {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid #ffc107;
}

.status-low {
  background: rgba(255, 152, 0, 0.2);
  color: #ff9800;
  border: 1px solid #ff9800;
}

.status-critical {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
  border: 1px solid #f44336;
}

.tank-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.stat-item {
  background: #242526;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #fff;
}

.tank-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.tank-visual-card {
  background: #3a3b3c;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.tank-hardware-card {
  grid-column: 1 / -1;
  margin-top: 24px;
}

/* Legacy styles for backward compatibility */
.tank-dashboard-card {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  align-items: center;
}

.tank-info {
  flex: 1;
  min-width: 250px;
}

.tank-info h3 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #61dafb;
}

.tank-details p {
  margin-bottom: 10px;
  font-size: 16px;
}

.read-value-btn {
  background-color: #ff9800;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  border: 1px solid transparent;
}

.read-value-btn:hover {
  background-color: #f57c00;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(255, 152, 0, 0.3);
}

.read-value-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(255, 152, 0, 0.2);
}

.read-value-btn:disabled {
  background-color: #666;
  color: #999;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.tank-visual {
  flex: 1;
  min-width: 250px;
  display: flex;
  justify-content: center;
}

.view-details-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background-color: #2196f3;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.view-details-btn:hover {
  background-color: #1976d2;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(33, 150, 243, 0.3);
  color: white;
  text-decoration: none;
}

.loading-indicator,
.error-message,
.no-data-message {
  padding: 20px;
  text-align: center;
  font-size: 16px;
}

.error-message {
  color: #f44336;
}

.mt-4 {
  margin-top: 30px;
}

/* System Overview */
.system-overview-section {
  margin-bottom: 40px;
}

.overview-header {
  text-align: center;
  margin-bottom: 32px;
}

.overview-title {
  color: #fff;
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #61dafb 0%, #21a9c7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.overview-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  margin: 0;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.overview-category {
  background: #3a3b3c;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.overview-category:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3);
  border-color: rgba(97, 218, 251, 0.3);
}

.category-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.overview-card {
  background: #242526;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  text-decoration: none;
  color: #fff;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.overview-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--card-color),
    transparent
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.overview-card:hover {
  transform: translateX(4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.2);
}

.overview-card:hover::before {
  opacity: 1;
}

.card-icon {
  font-size: 24px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  flex-shrink: 0;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex-grow: 1;
}

.card-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.card-value {
  font-size: 24px;
  font-weight: 700;
  color: #fff;
}

/* Card-specific colors */
.customers-card {
  --card-color: #4caf50;
}
.employees-card {
  --card-color: #2196f3;
}
.cities-card {
  --card-color: #ff9800;
}
.paid-bills-card {
  --card-color: #4caf50;
}
.unpaid-bills-card {
  --card-color: #f44336;
}

.customers-card:hover .card-icon {
  background: rgba(76, 175, 80, 0.2);
}
.employees-card:hover .card-icon {
  background: rgba(33, 150, 243, 0.2);
}
.cities-card:hover .card-icon {
  background: rgba(255, 152, 0, 0.2);
}
.paid-bills-card:hover .card-icon {
  background: rgba(76, 175, 80, 0.2);
}
.unpaid-bills-card:hover .card-icon {
  background: rgba(244, 67, 54, 0.2);
}

/* Legacy Dashboard Stats (for backward compatibility) */
.dashboard_row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-bottom: 20px;
}

.dashboard_data_div {
  background-color: #242526;
  color: #fff;
  border-radius: 10px;
  padding: 20px;
  transition: transform 0.3s, box-shadow 0.3s;
}

.dashboard_data_div:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* For Tablets (768px - 1024px) */
@media (max-width: 1024px) {
  .overview-title {
    font-size: 24px;
  }

  .overview-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }

  .overview-category {
    padding: 20px;
  }

  .category-title {
    font-size: 16px;
  }

  .card-value {
    font-size: 20px;
  }

  .dashboard_row {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .tank-dashboard-layout {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .tank-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .tank-actions {
    justify-content: center;
  }

  .tank-dashboard-card {
    flex-direction: column;
  }

  .tank-info,
  .tank-visual {
    width: 100%;
  }
}

/* For Mobile (max-width: 768px) */
@media (max-width: 768px) {
  .overview-header {
    margin-bottom: 24px;
  }

  .overview-title {
    font-size: 22px;
  }

  .overview-subtitle {
    font-size: 14px;
  }

  .overview-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .overview-category {
    padding: 16px;
  }

  .category-title {
    font-size: 15px;
    margin-bottom: 12px;
  }

  .overview-card {
    padding: 12px;
    gap: 12px;
  }

  .card-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .card-label {
    font-size: 13px;
  }

  .card-value {
    font-size: 18px;
  }

  .section-title {
    font-size: 20px;
  }

  .dashboard_row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .tank-dashboard-layout {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .tank-overview-card,
  .tank-visual-card {
    padding: 16px;
  }

  .tank-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .tank-header h3 {
    font-size: 20px;
  }

  .tank-stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stat-item {
    padding: 12px;
  }

  .stat-value {
    font-size: 18px;
  }

  .tank-actions {
    flex-direction: column;
    gap: 12px;
  }

  .read-value-btn,
  .view-details-btn {
    width: 100%;
    justify-content: center;
    padding: 14px;
  }

  .tank-info h3 {
    font-size: 18px;
  }

  .tank-details p {
    font-size: 14px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .pump-water-btn {
    width: 100%;
    justify-content: center;
    padding: 12px;
  }
}
